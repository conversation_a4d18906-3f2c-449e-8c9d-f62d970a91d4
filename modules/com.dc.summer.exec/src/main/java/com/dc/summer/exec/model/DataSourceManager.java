package com.dc.summer.exec.model;

import com.dc.summer.model.DBPDataSourceContainer;

import java.util.Collection;

public interface DataSourceManager extends DBPDataSourceContainer {

    Collection<? extends ConnectionManager> getAllConnectionManagers();

    void refreshDataSource(String connectionId, String userName, String serverName, long refreshTime);

    /**
     * 通过参数控制是否关闭数据源
     * @param connectionId 数据源连接ID
     * @param forceClose true：硬关闭；false：软关闭。
     * @return 数据源关闭是否成功
     */
    boolean closeDataSource(String connectionId, boolean forceClose);

    void closeExpiredDataSource();

}
