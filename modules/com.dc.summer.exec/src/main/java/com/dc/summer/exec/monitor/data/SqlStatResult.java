package com.dc.summer.exec.monitor.data;

import lombok.Data;

@Data
public class SqlStatResult {

    private String sql;

    private Long executeCount;

    private Long executeTime;

    private long slowest;

    private Integer transactionExecution;

    private Integer errorCount;

    private Long renewRowCount;

    private Long readRowCount;

    private Integer ongoing;

    private int maxConcurrent;

    private long[] executeTimePart = new long[8];

    private long[] executeRsTimePart = new long[8];

    private long[] readRowPart = new long[6];

    private long[] renewRowPart = new long[6];

}
