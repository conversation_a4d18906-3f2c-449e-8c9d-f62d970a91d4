package com.dc.summer.exec.monitor.impl;

import com.dc.summer.exec.monitor.data.StatParam;
import com.dc.summer.exec.monitor.AbstractCacheStatMonitor;
import com.dc.summer.exec.model.type.RecordType;
import com.dc.summer.exec.handler.RecordHandler;
import com.dc.summer.exec.monitor.data.SqlStatResult;
import com.dc.utils.ArithmeticUtils;
import com.dc.utils.bean.CloneUtils;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

public class SqlMonitor extends AbstractCacheStatMonitor<SqlStatResult> {

    @Override
    protected void execute() {

        Map<String, SqlStatResult> resultMap = CloneUtils.transListToMap(super.read(),
                cord -> ((SqlStatResult) cord.getRow()).getSql(), cord -> ((SqlStatResult) cord.getRow()));

        RecordHandler handle = RecordHandler.handle(RecordType.SQL);
        List<RecordHandler.Record> list = handle.extractRow(StatParam.class);
        super.cachesToList(list);
        list.stream()
                .filter(cord -> cord.getRow() != null && ((StatParam) cord.getRow()).getContainerId() != null)
                .collect(Collectors.groupingBy(RecordHandler.Record::getId))
                .values()
                .forEach(records -> {

                    RecordHandler.Record start = null;
                    RecordHandler.Record read = null;
                    RecordHandler.Record renew = null;
                    Long currentExecuteTime = null;

                    for (RecordHandler.Record cord : records) {
                        StatParam param = (StatParam) cord.getRow();
                        Long row = param.getRow();
                        String sql = cutOutSql(param.getSql());
                        if (sql == null) {
                            list.remove(cord);
                            continue;
                        }
                        resultMap.putIfAbsent(sql, new SqlStatResult());
                        SqlStatResult result = resultMap.get(sql);
                        result.setSql(sql);

                        switch (cord.getSign()) {
                            case EXECUTE_START:
                                start = cord;
                                result.setOngoing(ArithmeticUtils.plus(result.getOngoing()));
                                break;
                            case READ_START:
                                read = cord;
                                break;
                            case RENEW_START:
                                renew = cord;
                                break;
                            case EXECUTE_OVER:
                                if (start != null) {
                                    long time = cord.getTime() - start.getTime();
                                    currentExecuteTime = time;
                                    result.setExecuteCount(ArithmeticUtils.plus(result.getExecuteCount()));
                                    result.setExecuteTime(ArithmeticUtils.plus(result.getExecuteTime(), time));
                                    result.setSlowest(Math.max(time, result.getSlowest()));
                                    result.setMaxConcurrent(Math.max(result.getOngoing(), result.getMaxConcurrent()));
                                    result.setOngoing(ArithmeticUtils.minus(result.getOngoing()));
                                    result.setExecuteTimePart(fill(result.getExecuteTimePart(), time));
                                    list.remove(start);
                                    list.remove(cord);
                                }
                                break;
                            case READ_OVER:
                                if (read != null) {
                                    long time = ArithmeticUtils.plus(cord.getTime() - read.getTime(), currentExecuteTime);
                                    result.setExecuteRsTimePart(fill(result.getExecuteRsTimePart(), time));
                                    result.setReadRowCount(ArithmeticUtils.plus(row, result.getReadRowCount()));
                                    result.setReadRowPart(fill(result.getReadRowPart(), time));
                                    list.remove(read);
                                    list.remove(cord);
                                }
                                break;
                            case RENEW_OVER:
                                if (renew != null) {
                                    long time = ArithmeticUtils.plus(cord.getTime() - renew.getTime(), currentExecuteTime);
                                    result.setExecuteRsTimePart(fill(result.getExecuteRsTimePart(), time));
                                    result.setRenewRowCount(ArithmeticUtils.plus(row, result.getRenewRowCount()));
                                    result.setRenewRowPart(fill(result.getRenewRowPart(), time));
                                    list.remove(renew);
                                    list.remove(cord);
                                }
                                break;
                            case EXECUTE_ERROR:
                                result.setErrorCount(ArithmeticUtils.plus(result.getErrorCount()));
                                result.setOngoing(ArithmeticUtils.minus(result.getOngoing()));
                                list.remove(start);
                                list.remove(cord);
                                break;
                            case EXECUTE_COMMIT:
                            case EXECUTE_ROLLBACK:
                                result.setTransactionExecution(ArithmeticUtils.plus(result.getTransactionExecution()));
                                list.remove(cord);
                                break;
                            default:
                        }
                    }
                });
        super.listToCaches(list);
        super.write(resultMap.values());
    }

    @Override
    protected RecordType getRecordType() {
        return RecordType.STAT_SQL;
    }

    @Override
    public String getName() {
        return "stat.sql";
    }

    private static String cutOutSql(String sql) {
        if (sql != null) {
            String lowerSql = sql.toLowerCase(Locale.ROOT);
            if (lowerSql.startsWith("insert into")) {
                return sql.substring(0, lowerSql.indexOf("values") + 6) + "(...)";
            } else if (lowerSql.startsWith("db.") && lowerSql.contains(".insert")) {
                return sql.substring(0, lowerSql.indexOf("(")) + "(...)";
            }
        }
        return sql;
    }

}
