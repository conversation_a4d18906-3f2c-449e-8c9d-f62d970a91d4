package com.dc.summer.exec.handler;

import com.dc.summer.DBException;
import com.dc.type.DatabaseType;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.impl.jdbc.JDBCSQLDialect;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.registry.DataSourceDescriptor;
import com.dc.summer.registry.DataSourceProviderRegistry;
import com.dc.summer.registry.driver.DriverDescriptor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;

@Slf4j
public class DefaultDataSourceHandler extends DataSourceDescriptor {

    private DefaultDataSourceHandler(String id, DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        super(id, driver, connectionInfo);
    }

    public static DefaultDataSourceHandler handle(DatabaseType databaseType) {
        if (databaseType == null) {
            throw new RuntimeException("没有找到指定类型的数据库！");
        }

        List<DriverDescriptor> drivers = DataSourceProviderRegistry
                .getInstance()
                .getDataSourceProvider(databaseType.getDataSourceId())
                .getDrivers();
        if (drivers == null || drivers.size() == 0) {
            throw new RuntimeException("没有找到合适的驱动！");
        }

        DefaultDataSourceHandler handler = new DefaultDataSourceHandler(
                UUID.randomUUID().toString(),
                drivers.get(0),
                new DBPConnectionConfiguration());
        handler.setTemporary(true);
        try {
            handler.openDataSource(new LoggingProgressMonitor(), false);
            // 由于初始化给了 false，内部的方言也需要初始化，所以提取到外部使用。
            DBPDataSource dataSource = handler.getDataSource();
            SQLDialect sqlDialect = dataSource.getSQLDialect();
            if (sqlDialect instanceof JDBCSQLDialect) {
                try {
                    // 初始化驱动程序设置
                    ((JDBCSQLDialect) sqlDialect).initDriverSettings(null, (JDBCDataSource) dataSource, null);
                } catch (Throwable e) {
                    log.error("Error initializing dialect driver settings", e);
                }
            }
        } catch (DBException e) {
            log.error("打开默认数据源失败！{}", e.getMessage());
        }
        return handler;
    }

}
