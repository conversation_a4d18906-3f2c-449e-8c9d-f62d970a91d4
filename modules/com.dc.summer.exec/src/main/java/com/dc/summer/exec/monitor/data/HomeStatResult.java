package com.dc.summer.exec.monitor.data;

import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
public class HomeStatResult {

    private String version;

    private Map<String, Integer> databases;

    private Map<String, Map<String, String>> drivers;

    private boolean resetEnable;

    private int resetCount;

    private String javaVMName;

    private String javaVersion;

    private String[] javaDriverPath;

    private String[] javaClassPath;

    private Date startTime;

}
