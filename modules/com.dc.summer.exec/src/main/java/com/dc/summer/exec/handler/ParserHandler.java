package com.dc.summer.exec.handler;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.impl.sql.BasicSQLDialect;
import com.dc.summer.model.sql.SQLDialect;
import com.dc.summer.model.sql.SQLUtils;

public class ParserHandler {

    @NotNull
    public static SQLDialect getSqlDialectFromConnection(DBPDataSourceContainer dataSourceContainer) {
        DBPDataSource dataSource = dataSourceContainer.getDataSource();
        SQLDialect dialect;
        if (dataSource != null) {
            dialect = SQLUtils.getDialectFromDataSource(dataSource);
        } else {
            try {
                dialect = dataSourceContainer.getScriptDialect().createInstance();
            } catch (DBException e) {
                try {
                    dialect = dataSourceContainer.getDriver().getProviderDescriptor().getScriptDialect().createInstance();
                } catch (DBException e1) {
                    dialect = BasicSQLDialect.INSTANCE;
                }
            }
        }
        return dialect;
    }

}
