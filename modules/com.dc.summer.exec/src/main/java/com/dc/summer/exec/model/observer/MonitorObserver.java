package com.dc.summer.exec.model.observer;

import com.dc.summer.exec.monitor.data.MonitorParam;
import com.dc.summer.exec.monitor.data.MonitorResult;
import com.dc.summer.exec.monitor.AbstractStatMonitor;

import java.util.Set;

public interface MonitorObserver {

    Set<Class<? extends AbstractStatMonitor<?>>> affectClassSet();

    MonitorParam getMonitorParam();

    void transitResult(String monitorName, MonitorResult monitorResult);

}
