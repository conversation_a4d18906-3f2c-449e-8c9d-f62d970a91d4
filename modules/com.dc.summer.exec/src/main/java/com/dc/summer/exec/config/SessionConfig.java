package com.dc.summer.exec.config;

import lombok.Data;

@Data
public class SessionConfig {

    private static SessionConfig instance = new SessionConfig();

    private Integer maximumContextSize;
    private Long keepAliveTime;
    private Integer heartBeatCount;
    private Integer queryTimeout;
    private Integer networkTimeout;
    private String appName;

    public static SessionConfig getInstance() {
        return instance;
    }

    public static void setInstance(SessionConfig instance) {
        SessionConfig.instance = instance;
    }

}
