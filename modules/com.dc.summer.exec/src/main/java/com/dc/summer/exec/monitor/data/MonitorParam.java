package com.dc.summer.exec.monitor.data;

import lombok.Data;

@Data
public class MonitorParam {

    private Boolean timely;

    private String keywords;

    private String condition;

    public MonitorParam() {
        this(null, null, null);
    }

    public MonitorParam(Boolean timely, String keywords, String condition) {
        if (timely == null) {
            timely = false;
        }
        this.timely = timely;
        this.keywords = keywords;
        this.condition = condition;
    }
}
