package com.dc.summer.exec.handler;

import com.dc.io.FastReader;
import com.dc.io.FastWriter;
import com.dc.summer.registry.center.Global;
import com.dc.summer.exec.handler.impl.EmptyRecordHandler;
import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.exec.model.type.RecordSignType;
import com.dc.summer.exec.model.type.RecordType;
import com.dc.utils.StringUtils;
import com.dc.utils.io.FileException;
import com.google.gson.Gson;
import lombok.Builder;
import lombok.Data;

import java.io.*;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class RecordHandler {

    private static final Map<RecordType, RecordHandler> MAP = new ConcurrentHashMap<>();

    private static final String DIR = "/record/";

    private final ReadWriteLock lock = new ReentrantReadWriteLock();

    private final File file;

    private final RecordType type;

    private final static String SEPARATOR = "<-=->";

    public RecordHandler(File file, RecordType type) {
        this.file = file;
        this.type = type;
    }

    public static RecordHandler handle(RecordType recordType, boolean activate) {
        return activate ? handle(recordType) : new EmptyRecordHandler();
    }

    public static RecordHandler handle(RecordType recordType) {
        if (recordType == null) {
            return null;
        }
        if (MAP.get(recordType) == null) {
            synchronized (RecordHandler.class) {
                if (MAP.get(recordType) == null) {
                    try {
                        final String path = Global.getROOT() + DIR;
                        File folder = new File(path);
                        if (!folder.exists()) {
                            boolean mkdirs = folder.mkdirs();
                        }
                        File file = new File(path + recordType.getFileName());
                        if (!file.exists()) {
                            boolean createNewFile = file.createNewFile();
                        }
                        RecordHandler recordHandler = new RecordHandler(file, recordType);
                        MAP.put(recordType, recordHandler);
                    } catch (IOException e) {
                        throw new FileException(recordType.getFileName() + "文件创建失败！", e);
                    }
                }
            }
        }
        return MAP.get(recordType);
    }

    public void appendRow(Object row, RecordSignType sign) {
        if (!(row instanceof String)) {
            Gson gson = new Gson();
            row = gson.toJson(row);
        }
        lock.readLock().lock();
        try (FastWriter writer = new FastWriter(file, true)) {
            writer.write(Record.to(row, type, sign.name()));
        } catch (Exception e) {
            throw new FileException("文件写入失败！", e);
        } finally {
            lock.readLock().unlock();
        }
    }

    public List<Record> extractRow(Class<?> clazz) {
        lock.writeLock().lock();
        File reFile;
        try {
            reFile = rename(file);
            boolean b = file.createNewFile();
        } catch (IOException e) {
            throw new FileException(file.getName() + "文件创建失败！", e);
        } finally {
            lock.writeLock().unlock();
        }
        List<Record> list = new LinkedList<>();
        try (FastReader reader = new FastReader(reFile)) {
            String str;
            while ((str = reader.read()) != null) {
                list.add(Record.of(str, type, clazz));
            }
        } catch (Exception e) {
            throw new FileException("文件读出失败！", e);
        } finally {
            boolean b = reFile.delete();
        }
        return list;
    }

    public void writeRow(Object... rows) {
        lock.writeLock().lock();
        try (FastWriter writer = new FastWriter(file, false)) {
            for (Object row : rows) {
                if (!(row instanceof String)) {
                    Gson gson = new Gson();
                    row = gson.toJson(row);
                    writer.write(Record.to(row, type, ""));
                }
            }
        } catch (Exception e) {
            throw new FileException("文件写入失败！", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public List<Record> readRow(Class<?> clazz) {
        lock.readLock().lock();
        List<Record> list = new LinkedList<>();
        try (FastReader reader = new FastReader(file)) {
            String str;
            while ((str = reader.read()) != null) {
                list.add(Record.of(str, type, clazz));
            }
        } catch (Exception e) {
            throw new FileException("文件读出失败！", e);
        } finally {
            lock.readLock().unlock();
        }
        return list;
    }

    private File rename(File file) {
        String path = file.getPath();
        int i = path.lastIndexOf('.');
        path = path.substring(0, i) + "-" + UUID.randomUUID() + path.substring(i);
        File reFile = new File(path);
        if (file.renameTo(reFile)) {
            return reFile;
        } else {
            throw new FileException(file.getName() + "文件重命名失败！");
        }
    }

    @Data
    @Builder
    public static class Record {
        private long time;
        private String name;
        private String id;
        private String thread;
        private RecordSignType sign;
        private Object row;

        public static String to(Object row, RecordType type, String sign) {
            return String.format("%s %s %s --- [%s] (%s) %s %s", System.currentTimeMillis(), type.name(), HandlerCounter.getExecuteId(), Thread.currentThread().getName(), sign, SEPARATOR, row);
        }

        public static Record of(String str, RecordType type, Class<?> clazz) {

            String row = StringUtils.substring(str, SEPARATOR).trim();

            return Record.builder()
                    .time(Long.parseLong(str.split(type.name())[0].trim()))
                    .name(type.name())
                    .id(StringUtils.substring(str, type.name(), "--- [").trim())
                    .thread(StringUtils.substring(str, "--- [", "] ("))
                    .sign(RecordSignType.of(StringUtils.substring(str, "] (", ") " + SEPARATOR)))
                    .row(clazz == String.class ? row : new Gson().fromJson(row, clazz))
                    .build();
        }
    }



}
