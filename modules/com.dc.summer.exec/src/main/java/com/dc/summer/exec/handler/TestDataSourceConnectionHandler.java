package com.dc.summer.exec.handler;

import com.dc.annotation.SQL;
import com.dc.summer.Log;
import com.dc.summer.exec.model.data.TestConnectionConfiguration;
import com.dc.summer.exec.model.observer.ContextSubject;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.model.struct.DBSInstance;
import com.dc.summer.registry.DataSourceDescriptor;
import com.dc.summer.registry.DataSourceProviderDescriptor;
import com.dc.summer.registry.DataSourceProviderRegistry;
import com.dc.summer.registry.driver.DriverDescriptor;
import com.dc.utils.verification.VerificationUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.io.Closeable;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class TestDataSourceConnectionHandler extends DataSourceDescriptor implements Closeable {

    public static final String FAKE = "fake";

    private static final LoggingProgressMonitor MONITOR = new LoggingProgressMonitor(Log.getLog(TestDataSourceConnectionHandler.class));

    @Getter
    private DriverDescriptor driverDescriptor;

    @Getter
    private DBCExecutionContext[] contexts;

    private final List<Exception> exceptions = new LinkedList<>();

    private TestDataSourceConnectionHandler(String id, DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        super(id, driver, connectionInfo);
    }

    public static TestDataSourceConnectionHandler handle(TestConnectionConfiguration configuration) {

        TestDataSourceConnectionHandler fake = new TestDataSourceConnectionHandler(FAKE, DriverDescriptor.NULL_DRIVER, null);

        if (configuration != null) {

            VerificationUtils.byAnnotation(configuration);

            configuration.setCheckDatabaseProduct(true);

            DataSourceProviderRegistry providerRegistry = DataSourceProviderRegistry.getInstance();
            DataSourceProviderDescriptor providerDescriptor = providerRegistry.getDataSourceProvider(configuration.getDatabaseType().getDataSourceId());

            List<DriverDescriptor> driverDescriptors = new LinkedList<>();

            String driverId = configuration.getDriverId();
            if (driverId != null && !driverId.isBlank()) {
                try {
                    DriverDescriptor driver = providerDescriptor.getDriver(driverId);
                    if (driver == null) {
                        throw new DBCException("没有找到驱动，ID为[" + driverId + "]！");
                    }
                    driverDescriptors.add(driver);
                } catch (Exception e) {
                    fake.addException(driverId, e);
                }
            }

            List<String> driverIds = configuration.getDatabaseType().getDriverIds();
            if (driverIds.isEmpty()) {
                driverDescriptors.addAll(providerDescriptor.getDrivers());
            } else {
                driverIds.forEach(id -> {
                    if (providerDescriptor.getDriver(id) != null) {
                        driverDescriptors.add(providerDescriptor.getDriver(id));
                    }
                });
            }

            configuration.setProviderProperty(DBConstants.INTERNAL_PROP_FOR_TEST, String.valueOf(true));

            ContextSubject.trigger(contextObserver -> contextObserver.fillingConfigurationPassword(configuration));

            for (DriverDescriptor driverDescriptor : driverDescriptors) {
                try {
                    TestDataSourceConnectionHandler handler = new TestDataSourceConnectionHandler(
                            "Test DataSource Connection",
                            driverDescriptor,
                            configuration);

                    handler.openDataSource(MONITOR, false);

                    DBSInstance instance = handler.getDataSource().getDefaultInstance();
                    DBCExecutionContext context = instance.getDefaultContext(MONITOR, true);

                    @SQL String testSql = configuration.getTestSql();
                    if (testSql != null && !testSql.isBlank()) {
                        DBExecUtils.execute(MONITOR, context, "Test SQL", testSql);
                    }

                    if (context != null && context.isConnected()) {
                        handler.driverDescriptor = driverDescriptor;
                        handler.contexts = instance.getAllContexts();
                        return handler;
                    }

                } catch (Exception e) {
                    log.error("Test DataSource Connection Error, id is {}", driverDescriptor.getId(), e);
                    fake.addException(driverDescriptor.getId(), e);
                }
            }

        }

        return fake;
    }

    public void addException(String driverId, Exception e) {
        log.error(driverId + " - 测试连接失败！", e);
        this.exceptions.add(e);
    }

    public boolean isConnected() {
        return !this.getId().equals(FAKE);
    }

    public List<String> getExceptionMessages() {
        return this.exceptions.stream().map(Throwable::getMessage).distinct().collect(Collectors.toList());
    }

    @Override
    public void close() {
        if (ArrayUtils.isEmpty(contexts)) {
            return;
        }
        for (DBCExecutionContext context : contexts) {
            if (context != null && context.isConnected()) {
                context.close();
            }
        }
    }
}
