package com.dc.summer.exec.handler.impl;

import com.dc.summer.exec.handler.RecordHandler;
import com.dc.summer.exec.model.type.RecordSignType;

import java.util.List;

public class EmptyRecordHandler extends RecordHandler {

    public EmptyRecordHandler() {
        super(null, null);
    }

    @Override
    public void appendRow(Object row, RecordSignType sign) {
        // none
    }

    @Override
    public List<Record> extractRow(Class<?> clazz) {
        // none
        return List.of();
    }

    @Override
    public void writeRow(Object... rows) {
        // none
    }


    @Override
    public List<Record> readRow(Class<?> clazz) {
        // none
        return List.of();
    }
}
