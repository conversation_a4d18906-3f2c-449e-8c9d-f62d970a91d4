package com.dc.summer.exec.handler;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.exec.model.StatementManager;
import com.dc.summer.model.impl.jdbc.exec.JDBCStatementImpl;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class StatementHandler implements StatementManager {

    private static final LoggingProgressMonitor MONITOR = new LoggingProgressMonitor(Log.getLog(StatementHandler.class));

    private static final Map<String, StatementHandler> INSTANCE_MAP = new ConcurrentHashMap<>();

    private final Map<Statement, String> statementProcessIdMap = new ConcurrentHashMap<>();

    private StatementHandler() {
    }

    public static boolean isRunning(String token) {
        if (token != null) {
            StatementHandler handler = INSTANCE_MAP.get(token);
            if (handler != null) {
                return !handler.statementProcessIdMap.isEmpty();
            }
        }
        return false;
    }

    public static StatementHandler handle(String token) {

        if (token == null) {
            return null;
        }
        if (INSTANCE_MAP.get(token) == null) {
            synchronized (StatementHandler.class) {
                if (INSTANCE_MAP.get(token) == null) {
                    INSTANCE_MAP.put(token, new StatementHandler());
                }
            }
        }
        HandlerCounter.setToken(token);
        return INSTANCE_MAP.get(token);
    }

    @Override
    public void login(Statement statement, String processId) {
        if (processId == null) {
            processId = "";
        }
        statementProcessIdMap.put(statement, processId);
    }

    @Override
    public void cancel() {

        List<Exception> exceptions = new ArrayList<>();

        log.info("取消 Statement 开始!");
        for (Statement statement : statementProcessIdMap.keySet()) {
            if (statement instanceof JDBCStatementImpl) {
                try {
                    ((JDBCStatementImpl<?>) statement).cancelBlock(MONITOR, null);
                    break;
                } catch (DBException e) {
                    exceptions.add(e);
                }
            }
        }
        exceptions.forEach(e -> log.error("取消 Statement 失败！", e));
        log.info("取消 Statement 结束!");

    }

    @Override
    public void logout(Statement statement, String processId) {
        if (processId != null) {
            String v = statementProcessIdMap.get(statement);
            if (!processId.equals(v)) {
                return;
            }
        }
        statementProcessIdMap.remove(statement);
    }
}
